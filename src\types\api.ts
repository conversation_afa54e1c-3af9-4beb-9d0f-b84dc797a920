/**
 * API 相关类型定义
 */

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页请求参数
export interface PageParams {
  page?: number;
  pageSize?: number;
  [key: string]: any;
}

// 分页响应数据
export interface PageResult<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 请求配置扩展
export interface RequestConfig {
  /** 是否显示loading */
  loading?: boolean;
  /** 是否显示错误提示 */
  errorMessage?: boolean;
  /** 是否显示成功提示 */
  successMessage?: boolean;
  /** 自定义成功提示文本 */
  successText?: string;
  /** 是否需要token */
  withToken?: boolean;
  /** 重试次数 */
  retry?: number;
  /** 请求超时时间 */
  timeout?: number;
}

// 用户登录请求
export interface UserLoginReq {
  username: string;
  password: string;
}

// 角色信息
export interface RoleRecord {
  id: number;
  name: string;
  key: string;
}

// 菜单信息
export interface MenuRecord {
  id: number;
  parentId: number;
  name: string;
  title: string;
  type: number; // 1-菜单 2-按钮 3-目录
  path: string;
  component: string;
  icon: string;
  children: MenuRecord[];
}

// 用户信息
export interface UserLoginRecord {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  phone: string;
  email: string;
  gender: number;
  status: number;
  roles: RoleRecord[];
}

// 授权信息
export interface Auth {
  accessToken: string;
  menus: MenuRecord[];
}

// 认证记录
export interface AuthRecord {
  userInfo: UserLoginRecord;
  authInfo: Auth;
}

// 登录响应
export type LoginResponse = ApiResponse<AuthRecord>;

// HTTP状态码枚举
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// 业务状态码枚举
export enum BusinessCode {
  SUCCESS = 0,
  FAIL = 1,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  VALIDATION_ERROR = 422,
  SERVER_ERROR = 500,
}

// 错误类型
export interface ApiError {
  code: number;
  message: string;
  data?: any;
  stack?: string;
}
