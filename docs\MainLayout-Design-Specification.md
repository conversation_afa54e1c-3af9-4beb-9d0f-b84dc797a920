# MainLayout.vue 设计规范文档

## 📋 概述

MainLayout.vue 是 iBanko Dashboard 的核心布局组件，基于 Ant Design Vue 构建，采用现代化的设计语言和完整的主题系统。

## 🏗️ 架构设计

### 技术栈
- **框架**: Vue 3 + TypeScript
- **UI库**: Ant Design Vue 4.x
- **样式**: Less + CSS Variables
- **主题**: 动态主题系统 (ThemeProvider)
- **响应式**: 基于断点的自适应设计

### 设计原则
- **一致性**: 统一的设计语言和交互模式
- **响应式**: 移动端优先的自适应设计
- **可访问性**: 符合 WCAG 标准的无障碍设计
- **性能**: 优化的动画和渲染性能

## 📐 布局结构

### 整体布局
```
┌─────────────────────────────────────────┐
│ MainLayout (a-layout)                   │
│ ┌─────────┐ ┌─────────────────────────┐ │
│ │ Sider   │ │ Main Content Layout     │ │
│ │ 220px   │ │ ┌─────────────────────┐ │ │
│ │         │ │ │ Header (64px)       │ │ │
│ │ Logo    │ │ ├─────────────────────┤ │ │
│ │ Menu    │ │ │ Content             │ │ │
│ │         │ │ │ (Dynamic Height)    │ │ │
│ │         │ │ ├─────────────────────┤ │ │
│ │         │ │ │ Footer (Auto)       │ │ │
│ └─────────┘ │ └─────────────────────┘ │ │
└─────────────────────────────────────────┘
```

### 组件层级
1. **根容器** (`main-layout`)
2. **侧边栏** (`layout-sider`)
   - Logo 区域 (`logo-container`)
   - 导航菜单 (`side-menu`)
3. **主内容区** (`main-content-layout`)
   - 顶部导航 (`layout-header`)
   - 内容区域 (`layout-content`)
   - 底部信息 (`layout-footer`)
4. **移动端遮罩** (`mobile-overlay`)

## 🎨 设计系统

### 尺寸规范
- **侧边栏宽度**: 220px (展开) / 80px (折叠)
- **头部高度**: 64px
- **圆角半径**: 12px (标准) / 20px (内容区)
- **间距系统**: 8px, 12px, 16px, 24px, 32px

### 响应式断点
- **Large**: ≥992px (桌面端)
- **Medium**: 768px-991px (平板端)
- **Small**: ≤767px (移动端)
- **Extra Small**: ≤576px (小屏移动端)

### 动画规范
- **缓动函数**: `cubic-bezier(0.25, 0.46, 0.45, 0.94)`
- **过渡时长**: 0.3s (快速) / 0.4s (标准) / 0.6s (慢速)
- **悬停效果**: `translateY(-2px)` + 阴影增强

## 🎯 核心特性

### Logo 区域设计
- **背景**: 三色渐变 (primary-bg → primary-border → bg-elevated)
- **文字**: 双色渐变 (primary → info)
- **动画**: 光泽扫过效果 (0.6s)
- **交互**: 悬停提升 + 图标缩放

### 导航菜单设计
- **样式**: 圆角卡片式设计
- **状态**: 悬停/选中/默认三种状态
- **指示器**: 左侧 4px 圆角条
- **动画**: 水平位移 + 阴影变化

### 顶部导航设计
- **搜索框**: 圆角输入框 + 主题色按钮
- **通知**: 徽章计数显示
- **用户信息**: 头像 + 姓名 + 下拉菜单
- **主题切换**: 灯泡图标切换

### 内容区域设计
- **容器**: 圆角卡片 + 顶部装饰条
- **装饰条**: 主题色到成功色的渐变
- **交互**: 悬停提升效果
- **路由**: fade-slide 过渡动画

## 🌈 主题集成

### CSS 变量使用
```css
/* 背景色系 */
--theme-bg-container
--theme-bg-elevated
--theme-bg-layout

/* 主题色系 */
--theme-primary
--theme-primary-bg
--theme-primary-border
--theme-primary-hover

/* 文字色系 */
--theme-text-base
--theme-text-secondary
--theme-text-tertiary
--theme-text-quaternary

/* 功能色系 */
--theme-success
--theme-info
--theme-warning
--theme-error

/* 填充色系 */
--theme-fill-quaternary
```

### 主题适配
- **默认主题**: 蓝色系，清新现代
- **暗色主题**: 深色背景，高对比度
- **紧凑主题**: 更小的间距和尺寸
- **自定义主题**: 用户自定义主色调

## 📱 响应式设计

### 移动端适配
- **侧边栏**: 抽屉式显示，支持手势操作
- **遮罩层**: 半透明背景，点击关闭
- **搜索框**: 小屏幕隐藏
- **面包屑**: 576px 以下隐藏
- **用户名**: 768px 以下隐藏

### 布局调整
- **桌面端**: 固定侧边栏 + 主内容区
- **平板端**: 可折叠侧边栏
- **移动端**: 抽屉式侧边栏 + 全屏内容

## 🔧 技术实现

### 状态管理
```typescript
// 响应式状态
const collapsed = ref(false)           // 侧边栏折叠状态
const selectedKeys = ref(["dashboard"]) // 选中的菜单项
const openKeys = ref([""])             // 展开的子菜单
const searchText = ref("")             // 搜索文本
const isDark = ref(false)              // 暗色主题状态
const isMobile = ref(false)            // 移动端状态
const mobileMenuVisible = ref(false)   // 移动端菜单显示状态
```

### 核心方法
- `toggleCollapsed()`: 切换侧边栏折叠状态
- `toggleTheme()`: 切换主题模式
- `handleSearch()`: 处理搜索逻辑
- `onBreakpoint()`: 响应式断点处理

### 动画定义
```css
/* 淡入淡出 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

/* 滑动过渡 */
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
```

## 📊 性能优化

### 渲染优化
- 使用 `v-if` 控制移动端遮罩层渲染
- 路由组件懒加载
- CSS 动画使用 GPU 加速

### 交互优化
- 防抖搜索输入
- 平滑的过渡动画
- 响应式断点监听

## 🎪 使用示例

### 基本使用
```vue
<template>
  <MainLayout>
    <router-view />
  </MainLayout>
</template>
```

### 自定义配置
```vue
<script setup>
// 可以通过 props 传递配置
// 或者通过全局状态管理
</script>
```

## 📝 维护指南

### 样式修改
- 优先使用主题变量而非硬编码颜色
- 保持动画的一致性
- 注意响应式断点的兼容性

### 功能扩展
- 新增菜单项需要更新路由配置
- 主题变量需要在 ThemeProvider 中定义
- 响应式适配需要考虑所有断点

---

*文档版本: v1.0.0*  
*最后更新: 2025-06-27*  
*维护者: iBanko Dashboard Team*
